import os
import numpy as np
import pandas as pd
import scipy.io as sio
from matplotlib import pyplot as plt
from scipy import signal
from scipy.fft import fft, fftfreq
from scipy.signal import welch, hilbert, resample_poly
import pywt
from sklearn.preprocessing import StandardScaler
import pickle
import warnings
import scipy.stats

warnings.filterwarnings('ignore')
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

class BearingDataProcessor:
    def __init__(self, source_data_path):
        self.source_data_path = source_data_path
        self.bearing_params = {
            'SKF6205': {'n': 9, 'd': 0.3126, 'D': 1.537},
            'SKF6203': {'n': 9, 'd': 0.2656, 'D': 1.122}
        }
        self.selected_files = []
        self.features_data = []
        self.window_size = 4096
        self.overlap_ratio = 0.5
        self.target_fs = 32000

    def get_fault_frequencies(self, rpm, bearing_type='SKF6205'):
        params = self.bearing_params[bearing_type]
        fr = rpm / 60
        bpfo = fr * (params['n'] / 2) * (1 - params['d'] / params['D'])
        bpfi = fr * (params['n'] / 2) * (1 + params['d'] / params['D'])
        bsf = fr * (params['D'] / params['d']) * (1 - (params['d'] / params['D']) ** 2)
        return {'BPFO': bpfo, 'BPFI': bpfi, 'BSF': bsf, 'FR': fr}

    def resample_signal(self, signal_data, original_fs, target_fs):
        if original_fs == target_fs:
            return signal_data

        if original_fs > target_fs:
            up = target_fs
            down = original_fs
            gcd = np.gcd(up, down)
            up = up // gcd
            down = down // gcd
        else:
            up = target_fs
            down = original_fs
            gcd = np.gcd(up, down)
            up = up // gcd
            down = down // gcd

        resampled = resample_poly(signal_data, up, down)
        return resampled

    def select_source_data(self):
        """筛选源域数据，包含12kHz和48kHz数据"""
        selected_files = {
            'Normal': [],
            'OR': [],
            'IR': [],
            'B': []
        }

        normal_files_48k = [
            {'path': '48kHz_Normal_data/N_0.mat', 'fs': 48000},
            {'path': '48kHz_Normal_data/N_1_(1772rpm).mat', 'fs': 48000},
            {'path': '48kHz_Normal_data/N_2_(1750rpm).mat', 'fs': 48000},
            {'path': '48kHz_Normal_data/N_3.mat', 'fs': 48000}
        ]
        selected_files['Normal'] = normal_files_48k

        or_files = []

        # 48kHz外圈故障数据
        or_48k_paths = [
            # Centered位置
            '48kHz_DE_data/OR/Centered/0007/OR007@6_0.mat',
            '48kHz_DE_data/OR/Centered/0007/OR007@6_1.mat',
            '48kHz_DE_data/OR/Centered/0007/OR007@6_2.mat',
            '48kHz_DE_data/OR/Centered/0007/OR007@6_3.mat',
            '48kHz_DE_data/OR/Centered/0014/OR014@6_0.mat',
            '48kHz_DE_data/OR/Centered/0014/OR014@6_1.mat',
            '48kHz_DE_data/OR/Centered/0014/OR014@6_2.mat',
            '48kHz_DE_data/OR/Centered/0014/OR014@6_3.mat',
            '48kHz_DE_data/OR/Centered/0021/OR021@6_0.mat',
            '48kHz_DE_data/OR/Centered/0021/OR021@6_1.mat',
            '48kHz_DE_data/OR/Centered/0021/OR021@6_2.mat',
            '48kHz_DE_data/OR/Centered/0021/OR021@6_3.mat',
            # Opposite位置
            '48kHz_DE_data/OR/Opposite/0007/OR007@12_0.mat',
            '48kHz_DE_data/OR/Opposite/0007/OR007@12_1.mat',
            '48kHz_DE_data/OR/Opposite/0007/OR007@12_2.mat',
            '48kHz_DE_data/OR/Opposite/0007/OR007@12_3.mat',
            '48kHz_DE_data/OR/Opposite/0021/OR021@12_0.mat',
            '48kHz_DE_data/OR/Opposite/0021/OR021@12_1.mat',
            '48kHz_DE_data/OR/Opposite/0021/OR021@12_2.mat',
            '48kHz_DE_data/OR/Opposite/0021/OR021@12_3.mat',
            # Orthogonal位置
            '48kHz_DE_data/OR/Orthogonal/0007/OR007@3_0.mat',
            '48kHz_DE_data/OR/Orthogonal/0007/OR007@3_1.mat',
            '48kHz_DE_data/OR/Orthogonal/0007/OR007@3_2.mat',
            '48kHz_DE_data/OR/Orthogonal/0007/OR007@3_3.mat',
            '48kHz_DE_data/OR/Orthogonal/0021/OR021@3_0.mat',
            '48kHz_DE_data/OR/Orthogonal/0021/OR021@3_1.mat',
            '48kHz_DE_data/OR/Orthogonal/0021/OR021@3_2.mat',
            '48kHz_DE_data/OR/Orthogonal/0021/OR021@3_3.mat'
        ]
        for path in or_48k_paths:
            or_files.append({'path': path, 'fs': 48000})

        # 12kHz外圈故障数据
        or_12k_paths = [
            # Centered位置
            '12kHz_DE_data/OR/Centered/0007/OR007@6_0.mat',
            '12kHz_DE_data/OR/Centered/0007/OR007@6_1.mat',
            '12kHz_DE_data/OR/Centered/0007/OR007@6_2.mat',
            '12kHz_DE_data/OR/Centered/0007/OR007@6_3.mat',
            '12kHz_DE_data/OR/Centered/0014/OR014@6_0.mat',
            '12kHz_DE_data/OR/Centered/0014/OR014@6_1.mat',
            '12kHz_DE_data/OR/Centered/0014/OR014@6_2.mat',
            '12kHz_DE_data/OR/Centered/0014/OR014@6_3.mat',
            '12kHz_DE_data/OR/Centered/0021/OR021@6_0.mat',
            '12kHz_DE_data/OR/Centered/0021/OR021@6_1.mat',
            '12kHz_DE_data/OR/Centered/0021/OR021@6_2.mat',
            '12kHz_DE_data/OR/Centered/0021/OR021@6_3.mat',
            # Opposite位置
            '12kHz_DE_data/OR/Opposite/0007/OR007@12_0.mat',
            '12kHz_DE_data/OR/Opposite/0007/OR007@12_1.mat',
            '12kHz_DE_data/OR/Opposite/0007/OR007@12_2.mat',
            '12kHz_DE_data/OR/Opposite/0007/OR007@12_3.mat',
            '12kHz_DE_data/OR/Opposite/0021/OR021@12_0.mat',
            '12kHz_DE_data/OR/Opposite/0021/OR021@12_1.mat',
            '12kHz_DE_data/OR/Opposite/0021/OR021@12_2.mat',
            '12kHz_DE_data/OR/Opposite/0021/OR021@12_3.mat',
            # Orthogonal位置
            '12kHz_DE_data/OR/Orthogonal/0007/OR007@3_0.mat',
            '12kHz_DE_data/OR/Orthogonal/0007/OR007@3_1.mat',
            '12kHz_DE_data/OR/Orthogonal/0007/OR007@3_2.mat',
            '12kHz_DE_data/OR/Orthogonal/0007/OR007@3_3.mat',
            '12kHz_DE_data/OR/Orthogonal/0021/OR021@3_0.mat',
            '12kHz_DE_data/OR/Orthogonal/0021/OR021@3_1.mat',
            '12kHz_DE_data/OR/Orthogonal/0021/OR021@3_2.mat',
            '12kHz_DE_data/OR/Orthogonal/0021/OR021@3_3.mat'
        ]
        for path in or_12k_paths:
            or_files.append({'path': path, 'fs': 12000})

        selected_files['OR'] = or_files

        # 内圈故障：混合采样率
        ir_files = []

        # 48kHz内圈故障数据
        ir_48k_paths = [
            '48kHz_DE_data/IR/0007/IR007_0.mat',
            '48kHz_DE_data/IR/0007/IR007_1.mat',
            '48kHz_DE_data/IR/0007/IR007_2.mat',
            '48kHz_DE_data/IR/0007/IR007_3.mat',
            '48kHz_DE_data/IR/0014/IR014_0.mat',
            '48kHz_DE_data/IR/0014/IR014_1.mat',
            '48kHz_DE_data/IR/0014/IR014_2.mat',
            '48kHz_DE_data/IR/0014/IR014_3.mat',
            '48kHz_DE_data/IR/0021/IR021_0.mat',
            '48kHz_DE_data/IR/0021/IR021_1.mat',
            '48kHz_DE_data/IR/0021/IR021_2.mat',
            '48kHz_DE_data/IR/0021/IR021_3.mat'
        ]
        for path in ir_48k_paths:
            ir_files.append({'path': path, 'fs': 48000})

        # 12kHz内圈故障数据
        ir_12k_paths = [
            '12kHz_DE_data/IR/0007/IR007_0.mat',
            '12kHz_DE_data/IR/0007/IR007_1.mat',
            '12kHz_DE_data/IR/0007/IR007_2.mat',
            '12kHz_DE_data/IR/0007/IR007_3.mat',
            '12kHz_DE_data/IR/0014/IR014_0.mat',
            '12kHz_DE_data/IR/0014/IR014_1.mat',
            '12kHz_DE_data/IR/0014/IR014_2.mat',
            '12kHz_DE_data/IR/0014/IR014_3.mat',
            '12kHz_DE_data/IR/0021/IR021_0.mat',
            '12kHz_DE_data/IR/0021/IR021_1.mat',
            '12kHz_DE_data/IR/0021/IR021_2.mat',
            '12kHz_DE_data/IR/0021/IR021_3.mat',
            '12kHz_DE_data/IR/0028/IR028_0_(1797rpm).mat',
            '12kHz_DE_data/IR/0028/IR028_1_(1772rpm).mat',
            '12kHz_DE_data/IR/0028/IR028_2_(1750rpm).mat',
            '12kHz_DE_data/IR/0028/IR028_3_(1730rpm).mat'
        ]
        for path in ir_12k_paths:
            ir_files.append({'path': path, 'fs': 12000})

        selected_files['IR'] = ir_files

        # 滚动体故障：混合采样率
        b_files = []

        # 48kHz滚动体故障数据
        b_48k_paths = [
            '48kHz_DE_data/B/0007/B007_0.mat',
            '48kHz_DE_data/B/0007/B007_1.mat',
            '48kHz_DE_data/B/0007/B007_2.mat',
            '48kHz_DE_data/B/0007/B007_3.mat',
            '48kHz_DE_data/B/0014/B014_0.mat',
            '48kHz_DE_data/B/0014/B014_1.mat',
            '48kHz_DE_data/B/0014/B014_2.mat',
            '48kHz_DE_data/B/0014/B014_3.mat',
            '48kHz_DE_data/B/0021/B021_0.mat',
            '48kHz_DE_data/B/0021/B021_1.mat',
            '48kHz_DE_data/B/0021/B021_2.mat',
            '48kHz_DE_data/B/0021/B021_3.mat'
        ]
        for path in b_48k_paths:
            b_files.append({'path': path, 'fs': 48000})

        # 12kHz滚动体故障数据
        b_12k_paths = [
            '12kHz_DE_data/B/0007/B007_0.mat',
            '12kHz_DE_data/B/0007/B007_1.mat',
            '12kHz_DE_data/B/0007/B007_2.mat',
            '12kHz_DE_data/B/0007/B007_3.mat',
            '12kHz_DE_data/B/0014/B014_0.mat',
            '12kHz_DE_data/B/0014/B014_1.mat',
            '12kHz_DE_data/B/0014/B014_2.mat',
            '12kHz_DE_data/B/0014/B014_3.mat',
            '12kHz_DE_data/B/0021/B021_0.mat',
            '12kHz_DE_data/B/0021/B021_1.mat',
            '12kHz_DE_data/B/0021/B021_2.mat',
            '12kHz_DE_data/B/0021/B021_3.mat',
            '12kHz_DE_data/B/0028/B028_0_(1797rpm).mat',
            '12kHz_DE_data/B/0028/B028_1_(1772rpm).mat',
            '12kHz_DE_data/B/0028/B028_2_(1750rpm).mat',
            '12kHz_DE_data/B/0028/B028_3_(1730rpm).mat'
        ]
        for path in b_12k_paths:
            b_files.append({'path': path, 'fs': 12000})

        selected_files['B'] = b_files

        self.selected_files = selected_files

        # 打印筛选结果
        for fault_type, files in selected_files.items():
            print(f"\n{fault_type}: {len(files)} 个文件")
            fs_48k = sum(1 for f in files if f['fs'] == 48000)
            fs_12k = sum(1 for f in files if f['fs'] == 12000)
            print(f"  - 48kHz: {fs_48k} 个文件")
            print(f"  - 12kHz: {fs_12k} 个文件")

        return selected_files

    def extract_samples_from_signal(self, signal_data, window_size=None, overlap_ratio=None):
        if window_size is None:
            window_size = self.window_size
        if overlap_ratio is None:
            overlap_ratio = self.overlap_ratio

        step_size = int(window_size * (1 - overlap_ratio))
        samples = []

        for start in range(0, len(signal_data) - window_size + 1, step_size):
            window = signal_data[start:start + window_size]
            samples.append(window)

        return samples

    def check_signal_quality(self, signal_data):
        if np.any(np.isnan(signal_data)) or np.any(np.isinf(signal_data)):
            return False

        signal_std = np.std(signal_data)
        if signal_std < 1e-8:
            return False

        signal_max = np.max(np.abs(signal_data))
        if signal_max > 100 * signal_std:
            return False

        return True

    def load_mat_file(self, file_path):
        full_path = os.path.join(self.source_data_path, file_path)
        try:
            mat_data = sio.loadmat(full_path)

            de_data, fe_data, ba_data, rpm_data = None, None, None, None

            for key in mat_data.keys():
                if not key.startswith('__'):
                    if 'DE_time' in key:
                        de_data = mat_data[key].flatten()
                    elif 'FE_time' in key:
                        fe_data = mat_data[key].flatten()
                    elif 'BA_time' in key:
                        ba_data = mat_data[key].flatten()
                    elif 'RPM' in key:
                        rpm_data = mat_data[key].flatten()[0] if len(mat_data[key].flatten()) > 0 else 1797

            return {
                'DE': de_data,
                'FE': fe_data,
                'BA': ba_data,
                'RPM': rpm_data if rpm_data is not None else 1797
            }
        except Exception as e:
            print(f"加载文件失败 {file_path}: {e}")
            return None

    def extract_time_domain_features(self, signal_data):
        features = {}
        features['mean'] = np.mean(signal_data)
        features['std'] = np.std(signal_data)
        features['var'] = np.var(signal_data)
        features['rms'] = np.sqrt(np.mean(signal_data ** 2))
        features['peak'] = np.max(np.abs(signal_data))
        features['peak_to_peak'] = np.ptp(signal_data)

        features['skewness'] = scipy.stats.skew(signal_data)
        features['kurtosis'] = scipy.stats.kurtosis(signal_data)

        mean_abs = np.mean(np.abs(signal_data))
        if mean_abs > 0:
            features['crest_factor'] = features['peak'] / features['rms'] if features['rms'] > 0 else 0
            features['impulse_factor'] = features['peak'] / mean_abs
            features['shape_factor'] = features['rms'] / mean_abs if mean_abs > 0 else 0
            sqrt_mean_sqrt = np.mean(np.sqrt(np.abs(signal_data)))
            features['clearance_factor'] = features['peak'] / (sqrt_mean_sqrt ** 2) if sqrt_mean_sqrt > 0 else 0
        else:
            features['crest_factor'] = 0
            features['impulse_factor'] = 0
            features['shape_factor'] = 0
            features['clearance_factor'] = 0

        return features

    def extract_frequency_domain_features(self, signal_data, fs, rpm):
        features = {}

        N = len(signal_data)
        fft_vals = fft(signal_data)
        freqs = fftfreq(N, 1 / fs)[:N // 2]
        magnitude = np.abs(fft_vals[:N // 2])
        total_magnitude = np.sum(magnitude)
        if total_magnitude == 0:
            return {
                'spectral_centroid': 0, 'spectral_variance': 0, 'spectral_skewness': 0, 'spectral_kurtosis': 0,
                'BPFO_amplitude': 0, 'BPFI_amplitude': 0, 'BSF_amplitude': 0, 'FR_amplitude': 0,
                'low_freq_energy_ratio': 0, 'mid_freq_energy_ratio': 0, 'high_freq_energy_ratio': 0
            }

        features['spectral_centroid'] = np.sum(freqs * magnitude) / total_magnitude
        spectral_variance = np.sum(((freqs - features['spectral_centroid']) ** 2) * magnitude) / total_magnitude
        features['spectral_variance'] = spectral_variance

        if spectral_variance > 0:
            features['spectral_skewness'] = np.sum(((freqs - features['spectral_centroid']) ** 3) * magnitude) / (
                    total_magnitude * spectral_variance ** 1.5)
            features['spectral_kurtosis'] = np.sum(((freqs - features['spectral_centroid']) ** 4) * magnitude) / (
                    total_magnitude * spectral_variance ** 2)
        else:
            features['spectral_skewness'] = 0
            features['spectral_kurtosis'] = 0

        try:
            fault_freqs = self.get_fault_frequencies(rpm)

            freq_resolution = fs / N

            for fault_type, freq in fault_freqs.items():
                if freq > 0 and freq < fs / 2:
                    freq_idx = np.argmin(np.abs(freqs - freq))

                    freq_range_hz = max(5.0, freq * 0.05)  # 频率的5%或5Hz，取较大值
                    freq_range_points = max(10, int(freq_range_hz / freq_resolution))

                    start_idx = max(0, freq_idx - freq_range_points // 2)
                    end_idx = min(len(magnitude), freq_idx + freq_range_points // 2 + 1)

                    if end_idx > start_idx:
                        search_magnitudes = magnitude[start_idx:end_idx]
                        max_amplitude = np.max(search_magnitudes)

                        features[f'{fault_type}_amplitude'] = max_amplitude

                    else:
                        features[f'{fault_type}_amplitude'] = 0

                else:
                    features[f'{fault_type}_amplitude'] = 0

        except Exception as e:
            features['BPFO_amplitude'] = 0
            features['BPFI_amplitude'] = 0
            features['BSF_amplitude'] = 0
            features['FR_amplitude'] = 0

        total_energy = np.sum(magnitude ** 2)
        if total_energy > 0:
            low_freq_idx = np.where(freqs <= 500)[0]
            features['low_freq_energy_ratio'] = np.sum(magnitude[low_freq_idx] ** 2) / total_energy if len(
                low_freq_idx) > 0 else 0

            mid_freq_idx = np.where((freqs > 500) & (freqs <= 5000))[0]
            features['mid_freq_energy_ratio'] = np.sum(magnitude[mid_freq_idx] ** 2) / total_energy if len(
                mid_freq_idx) > 0 else 0

            high_freq_idx = np.where(freqs > 5000)[0]
            features['high_freq_energy_ratio'] = np.sum(magnitude[high_freq_idx] ** 2) / total_energy if len(
                high_freq_idx) > 0 else 0
        else:
            features['low_freq_energy_ratio'] = 0
            features['mid_freq_energy_ratio'] = 0
            features['high_freq_energy_ratio'] = 0

        return features

    def extract_time_frequency_features(self, signal_data, fs):
        features = {}

        try:
            wavelet = 'db4'
            levels = 4
            wp = pywt.WaveletPacket(signal_data, wavelet, maxlevel=levels)

            energy_features = []
            for i in range(2 ** levels):
                try:
                    node_name = [node.path for node in wp.get_level(levels, 'freq')][i]
                    coeffs = wp[node_name].data
                    energy = np.sum(coeffs ** 2)
                    energy_features.append(energy)
                except:
                    energy_features.append(0)

            total_energy = sum(energy_features)
            if total_energy > 0:
                for i, energy in enumerate(energy_features):
                    features[f'wavelet_energy_band_{i}'] = energy / total_energy

                energy_ratios = np.array(energy_features) / total_energy
                energy_ratios = energy_ratios[energy_ratios > 0]
                if len(energy_ratios) > 0:
                    features['wavelet_entropy'] = -np.sum(energy_ratios * np.log2(energy_ratios + 1e-12))
                else:
                    features['wavelet_entropy'] = 0
            else:
                for i in range(2 ** levels):
                    features[f'wavelet_energy_band_{i}'] = 0
                features['wavelet_entropy'] = 0

            analytic_signal = hilbert(signal_data)
            envelope = np.abs(analytic_signal)

            features['envelope_mean'] = np.mean(envelope)
            features['envelope_std'] = np.std(envelope)
            features['envelope_skewness'] = scipy.stats.skew(envelope)
            features['envelope_kurtosis'] = scipy.stats.kurtosis(envelope)

        except Exception as e:
            for i in range(16):
                features[f'wavelet_energy_band_{i}'] = 0
            features['wavelet_entropy'] = 0
            features['envelope_mean'] = 0
            features['envelope_std'] = 0
            features['envelope_skewness'] = 0
            features['envelope_kurtosis'] = 0

        return features

    def extract_features(self, signal_data, sensor_type, fs, rpm, file_info):
        features = {}

        features['file_path'] = file_info['file_path']
        features['fault_type'] = file_info['fault_type']
        features['sensor_type'] = sensor_type
        features['rpm'] = rpm
        features['original_fs'] = file_info['original_fs']
        features['resampled_fs'] = fs
        signal_data = signal_data - np.mean(signal_data)

        time_features = self.extract_time_domain_features(signal_data)
        for key, value in time_features.items():
            features[f'{sensor_type}_{key}'] = value

        freq_features = self.extract_frequency_domain_features(signal_data, fs, rpm)
        for key, value in freq_features.items():
            features[f'{sensor_type}_{key}'] = value

        time_freq_features = self.extract_time_frequency_features(signal_data, fs)
        for key, value in time_freq_features.items():
            features[f'{sensor_type}_{key}'] = value

        return features

    def detect_outliers_by_category(self, df, method='iqr', iqr_factor=1.5, z_threshold=3):
        outlier_indices = {}
        numeric_cols = df.select_dtypes(include=[np.number]).columns.tolist()
        exclude_cols = ['rpm', 'original_fs', 'resampled_fs']
        feature_cols = [col for col in numeric_cols if col not in exclude_cols]

        for fault_type in ['Normal', 'OR', 'IR', 'B']:
            category_data = df[df['fault_type'] == fault_type]
            if category_data.empty:
                outlier_indices[fault_type] = []
                continue

            print(f"\n检测 {fault_type} 类别异常值 (样本数: {len(category_data)})")

            category_outliers = set()

            feature_data = category_data[feature_cols]

            if method == 'iqr' or method == 'both':
                for col in feature_cols:
                    col_data = feature_data[col]
                    Q1 = col_data.quantile(0.25)
                    Q3 = col_data.quantile(0.75)
                    IQR = Q3 - Q1

                    if IQR > 0:
                        lower_bound = Q1 - iqr_factor * IQR
                        upper_bound = Q3 + iqr_factor * IQR

                        outliers = col_data[(col_data < lower_bound) | (col_data > upper_bound)].index
                        category_outliers.update(outliers)

            if method == 'zscore' or method == 'both':
                from scipy import stats
                z_scores = np.abs(stats.zscore(feature_data))
                outlier_mask = (z_scores > z_threshold).any(axis=1)
                z_outliers = feature_data[outlier_mask].index
                category_outliers.update(z_outliers)

            outlier_indices[fault_type] = list(category_outliers)
            print(
                f"  检测到 {len(category_outliers)} 个异常值 ({len(category_outliers) / len(category_data) * 100:.2f}%)")

        return outlier_indices

    def remove_outliers(self, method='iqr', iqr_factor=1.5, z_threshold=3, max_removal_ratio=0.1):
        if not self.features_data:
            print("没有特征数据可处理")
            return

        # 转换为DataFrame
        df = pd.DataFrame(self.features_data)
        original_counts = df['fault_type'].value_counts()

        print(f"\n原始数据分布:")
        for fault_type in ['Normal', 'OR', 'IR', 'B']:
            count = original_counts.get(fault_type, 0)
            print(f"  {fault_type}: {count}")

        # 检测异常值
        outlier_indices = self.detect_outliers_by_category(df, method, iqr_factor, z_threshold)

        # 应用移除比例限制并移除异常值
        indices_to_remove = []
        removal_summary = {}

        for fault_type in ['Normal', 'OR', 'IR', 'B']:
            outliers = outlier_indices[fault_type]
            original_count = original_counts.get(fault_type, 0)

            if original_count == 0:
                removal_summary[fault_type] = {'removed': 0, 'remaining': 0, 'removal_ratio': 0}
                continue

            # 计算允许移除的最大数量
            max_removal = int(original_count * max_removal_ratio)

            if len(outliers) > max_removal:
                outliers = np.random.choice(outliers, max_removal, replace=False).tolist()

            indices_to_remove.extend(outliers)
            remaining_count = original_count - len(outliers)
            removal_ratio = len(outliers) / original_count

            removal_summary[fault_type] = {
                'removed': len(outliers),
                'remaining': remaining_count,
                'removal_ratio': removal_ratio
            }

        # 移除异常值
        df_cleaned = df.drop(indices_to_remove).reset_index(drop=True)

        # 更新特征数据
        self.features_data = df_cleaned.to_dict('records')

        # 打印移除结果
        print(f"\n异常值移除结果:")
        print(f"{'类别':<8} {'原始':<8} {'移除':<8} {'剩余':<8} {'移除率':<8}")
        print("-" * 50)

        for fault_type in ['Normal', 'OR', 'IR', 'B']:
            summary = removal_summary[fault_type]
            original = original_counts.get(fault_type, 0)
            print(
                f"{fault_type:<8} {original:<8} {summary['removed']:<8} {summary['remaining']:<8} {summary['removal_ratio'] * 100:<7.1f}%")

        total_original = original_counts.sum()
        total_removed = sum(summary['removed'] for summary in removal_summary.values())
        total_remaining = len(df_cleaned)
        overall_removal_ratio = total_removed / total_original if total_original > 0 else 0

        print("-" * 50)
        print(
            f"{'总计':<8} {total_original:<8} {total_removed:<8} {total_remaining:<8} {overall_removal_ratio * 100:<7.1f}%")

        print(f"\n数据清理完成:")
        print(f"  原始样本数: {total_original}")
        print(f"  移除样本数: {total_removed}")
        print(f"  保留样本数: {total_remaining}")
        print(f"  整体移除率: {overall_removal_ratio * 100:.2f}%")

    def process_all_selected_files(self):
        print("\n" + "=" * 60)
        print("开始特征提取（混合采样率 + 重采样）")
        print("=" * 60)
        print(f"目标采样率: {self.target_fs} Hz")
        print(f"窗口参数: 窗口大小={self.window_size}, 重叠率={self.overlap_ratio}")

        all_features = []
        total_samples = 0

        for fault_type, file_list in self.selected_files.items():
            fault_samples = 0

            for file_info in file_list:
                file_path = file_info['path']
                original_fs = file_info['fs']

                # 加载文件
                mat_data = self.load_mat_file(file_path)
                if mat_data is None:
                    continue

                rpm = mat_data['RPM']

                # 处理DE数据（主要传感器）
                if mat_data['DE'] is not None:
                    signal_length = len(mat_data['DE'])
                    print(f"    原始信号长度: {signal_length} 点 ({signal_length / original_fs:.2f} 秒)")

                    # 重采样到目标采样率
                    resampled_signal = self.resample_signal(mat_data['DE'], original_fs, self.target_fs)
                    print(
                        f"    重采样后长度: {len(resampled_signal)} 点 ({len(resampled_signal) / self.target_fs:.2f} 秒)")

                    # 分割信号为多个样本
                    samples = self.extract_samples_from_signal(resampled_signal)
                    valid_samples = 0

                    for i, sample in enumerate(samples):
                        # 检查样本质量
                        if not self.check_signal_quality(sample):
                            continue

                        file_info_dict = {
                            'file_path': f"{file_path}_sample_{i}",
                            'fault_type': fault_type,
                            'original_fs': original_fs
                        }

                        # 提取特征
                        features = self.extract_features(sample, 'DE', self.target_fs, rpm, file_info_dict)
                        all_features.append(features)
                        valid_samples += 1

                    print(f"    从DE信号中提取了 {valid_samples} 个有效样本")
                    fault_samples += valid_samples

            total_samples += fault_samples

        self.features_data = all_features
        print(f"\n特征提取完成:")
        print(f"  总样本数: {len(all_features)}")

        if all_features:
            df_temp = pd.DataFrame(all_features)
            print(f"\n各类别样本数:")
            for fault_type in ['Normal', 'OR', 'IR', 'B']:
                count = len(df_temp[df_temp['fault_type'] == fault_type])
                print(f"    {fault_type}: {count}")

            # 打印原始采样率分布
            print(f"\n原始采样率分布:")
            fs_counts = df_temp['original_fs'].value_counts()
            for fs, count in fs_counts.items():
                print(f"    {fs} Hz: {count}")

        return all_features

    def save_processed_data(self, output_dir='processed_data_mixed_fs'):
        os.makedirs(output_dir, exist_ok=True)

        # 保存筛选的文件列表
        with open(os.path.join(output_dir, 'selected_files.pickle'), 'wb') as f:
            pickle.dump(self.selected_files, f)

        # 保存处理参数
        processing_params = {
            'window_size': self.window_size,
            'overlap_ratio': self.overlap_ratio,
            'target_fs': self.target_fs,
            'original_fs_list': [12000, 48000]
        }
        with open(os.path.join(output_dir, 'processing_params.pickle'), 'wb') as f:
            pickle.dump(processing_params, f)

        if self.features_data:
            df = pd.DataFrame(self.features_data)
            df.to_csv(os.path.join(output_dir, 'extracted_features.csv'), index=False)
            df.to_pickle(os.path.join(output_dir, 'extracted_features.pkl'))

            # 保存特征统计信息
            feature_stats = df.describe()
            feature_stats.to_csv(os.path.join(output_dir, 'feature_statistics.csv'))

            # 按故障类型分别保存
            for fault_type in ['Normal', 'OR', 'IR', 'B']:
                fault_data = df[df['fault_type'] == fault_type]
                if not fault_data.empty:
                    fault_data.to_csv(os.path.join(output_dir, f'features_{fault_type}.csv'), index=False)

            print(f"\n数据集摘要:")
            print(f"  总样本数: {len(df)}")
            print(f"  特征维度: {len(df.columns)}")
            print("  各类样本数:")
            for fault_type in ['Normal', 'OR', 'IR', 'B']:
                count = len(df[df['fault_type'] == fault_type])
                percentage = count / len(df) * 100
                print(f"    {fault_type}: {count} ({percentage:.1f}%)")

        return output_dir


def main():

    source_data_path = r"源域数据集"

    processor = BearingDataProcessor(source_data_path)

    processor.window_size = 4096
    processor.overlap_ratio = 0.5
    processor.target_fs = 32000

    processor.select_source_data()
    processor.process_all_selected_files()
    processor.remove_outliers(
        method='both',
        iqr_factor=1.5,
        z_threshold=3,
        max_removal_ratio=0.1
    )


if __name__ == "__main__":
    main()